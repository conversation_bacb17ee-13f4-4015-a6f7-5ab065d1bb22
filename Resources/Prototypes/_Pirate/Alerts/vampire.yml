- type: alert
  id: VampireBlood
  icons:
  - sprite: /Textures/_Pirate/Interface/Alerts/blood_counter.rsi
    state: background
  alertViewEntity: AlertVampireBloodSpriteView
  name: alerts-vampire-blood-name
  description: alerts-vampire-blood-desc

- type: entity
  id: AlertVampireBloodSpriteView
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: /Textures/_Pirate/Interface/Alerts/blood_counter.rsi
    layers:
    - map: [ "enum.AlertVisualLayers.Base" ]
    - map: [ "enum.VampireVisualLayers.Digit1" ]
      offset: -0.35, 0
    - map: [ "enum.VampireVisualLayers.Digit2" ]
      offset: -0.150, 0
    - map: [ "enum.VampireVisualLayers.Digit3" ]

- type: alert
  id: VampireStellarWeakness
  icons:
  - sprite: /Textures/_Pirate/Interface/Actions/actions_vampire.rsi
    state: stellarweakness
  alertViewEntity: AlertVampireStellarWeaknessSpriteView
  name: alerts-vampire-stellar-weakness-name
  description: alerts-vampire-stellar-weakness-desc

- type: entity
  id: AlertVampireStellarWeaknessSpriteView
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: /Textures/_Pirate/Interface/Actions/actions_vampire.rsi
    layers:
    - map: [ "enum.AlertVisualLayers.Base" ]
