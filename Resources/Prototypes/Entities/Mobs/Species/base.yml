# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2023 notquitehadouken <1isthisameme>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 whateverusername0 <whateveremail>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  save: false
  parent:
  - BaseMob
  - MobDamageable
  - MobPolymorphable
  - MobCombat
  - StripableInventoryBase
  id: BaseMobSpecies
  abstract: true
  components:
  - type: Sprite
    layers:
    - map: [ "enum.HumanoidVisualLayers.Chest" ]
    - map: [ "enum.HumanoidVisualLayers.Groin" ]
    - map: [ "enum.HumanoidVisualLayers.Head" ]
    - map: [ "enum.HumanoidVisualLayers.Snout" ]
    - map: [ "enum.HumanoidVisualLayers.Eyes" ]
    - map: [ "enum.HumanoidVisualLayers.RArm" ]
    - map: [ "enum.HumanoidVisualLayers.LArm" ]
    - map: [ "enum.HumanoidVisualLayers.RLeg" ]
    - map: [ "enum.HumanoidVisualLayers.LLeg" ]
    - map: [ "enum.HumanoidVisualLayers.Underwear" ]
    - map: [ "enum.HumanoidVisualLayers.Undershirt" ]
    - map: ["jumpsuit"]
    - map: ["enum.HumanoidVisualLayers.LFoot"]
    - map: ["enum.HumanoidVisualLayers.RFoot"]
    - map: ["enum.HumanoidVisualLayers.LHand"]
    - map: ["enum.HumanoidVisualLayers.RHand"]
    - map: [ "gloves" ]
    - map: [ "shoes" ]
    - map: [ "ears" ]
    - map: [ "eyes" ]
    - map: [ "id" ]
    - map: [ "outerClothing" ]
    - map: [ "belt" ] #Goobedit - Belts over outerwear
    - map: [ "back" ]
    - map: [ "neck" ]
    - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
    - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
    - map: [ "maskalt" ]
    - map: [ "enum.HumanoidVisualLayers.Hair" ]
    - map: [ "mask" ]
    - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
    - map: [ "enum.HumanoidVisualLayers.Tail" ]
    - map: [ "enum.HumanoidVisualLayers.Wings" ] # For IPC wings porting from SimpleStation
    - map: [ "head" ]
    - map: [ "pocket1" ]
    - map: [ "pocket2" ]
    - map: ["enum.HumanoidVisualLayers.Handcuffs"]
      color: "#ffffff"
      sprite: Objects/Misc/handcuffs.rsi
      state: body-overlay-2
      visible: false
    - map: [ "clownedon" ] # Dynamically generated
      sprite: "Effects/creampie.rsi"
      state: "creampie_human"
      visible: false
#  - type: DamageVisuals
#    thresholds: [ 10, 20, 30, 50, 70, 100 ]
#    targetLayers:
#      - "enum.HumanoidVisualLayers.Chest"
#      - "enum.HumanoidVisualLayers.Head"
#      - "enum.HumanoidVisualLayers.LArm"
#      - "enum.HumanoidVisualLayers.LLeg"
#    - "enum.HumanoidVisualLayers.RArm"
#    - "enum.HumanoidVisualLayers.RLeg"
#    damageOverlayGroups:
#      Brute:
#        sprite: _Shitmed/Mobs/Effects/brute_damage.rsi
#        color: "#FF0000"
#      Burn:
#        sprite: _Shitmed/Mobs/Effects/burn_damage.rsi
  - type: GenericVisualizer
    visuals:
      enum.CreamPiedVisuals.Creamed:
        clownedon: # Not 'creampied' bc I can already see Skyrat complaining about conflicts.
          True: {visible: true}
          False: {visible: false}
  - type: StatusIcon
    bounds: -0.5,-0.5,0.5,0.5
  - type: HasJobIcons # Goobstation
  - type: RotationVisuals
    defaultRotation: 90
    horizontalRotation: 90
  - type: HumanoidAppearance
    species: Human
  - type: SlowOnDamage
    speedModifierThresholds:
      60: 0.7
      80: 0.5
  - type: Fixtures
    fixtures: # TODO: This needs a second fixture just for mob collisions.
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.35
        density: 185
        restitution: 0.0
        mask:
        - MobMask
        layer:
        - MobLayer
  - type: FloorOcclusion
  - type: RangedDamageSound
    soundGroups:
      Brute:
        collection:
          MeatBulletImpact
    soundTypes:
      Heat:
        collection:
          MeatLaserImpact
  - type: Reactive
    groups:
      Flammable: [ Touch ]
      Extinguish: [ Touch ]
      Acidic: [Touch, Ingestion, Eyes] # Goobstation
    reactions:
    - reagents: [Water, SpaceCleaner]
      methods: [Touch]
      effects:
      - !type:WashCreamPieReaction
  - type: StatusEffects
    allowed:
    - Stun
    - KnockedDown
    - SlowedDown
    - Stutter
    - Electrocution
    - Drunk
    - SlurredSpeech
    - RatvarianLanguage
    - PressureImmunity
    - Muted
    - TemporaryBlindness
    - Pacified
    - StaminaModifier
    - Flashed
    - RadiationProtection
    - Vulgarity # Goobstation - add Profanol
    - Brainrotted # Goobstation - skibidi rizz ohio update
    - Adrenaline
    - Brainrotted # Goobstation - skibidi rizz ohio update
    - Dementia # Goobstation - Amnestic spray!
  - type: Body
    prototype: Human
    requiredLegs: 2
  - type: Identity
  - type: IdExaminable
  - type: Hands
  - type: ComplexInteraction
  - type: Internals
  - type: FloatingVisuals
  - type: Climbing
  - type: Cuffable
  - type: Ensnareable
    sprite: Objects/Misc/ensnare.rsi
    state: icon
  - type: AnimationPlayer
  - type: Buckle
  - type: CombatMode
    canDisarm: true
  - type: MeleeWeapon
    soundHit:
      collection: Punch
    angle: 30
    animation: WeaponArcFist
    attackRate: 1
    damage:
      types:
        Blunt: 5
  - type: SleepEmitSound
  - type: SSDIndicator
  - type: StandingState
  - type: Dna
  - type: Scent # Einstein Engines
  - type: MindContainer
    showExamineInfo: true
  - type: CanEnterCryostorage
  - type: InteractionPopup
    successChance: 1
    interactSuccessString: hugging-success-generic
    interactSuccessSound: /Audio/Effects/thudswoosh.ogg
    messagePerceivedByOthers: hugging-success-generic-others
  - type: CanHostGuardian
  - type: NpcFactionMember
    factions:
    - NanoTrasen
  - type: CreamPied
  - type: Stripping
  - type: AccessReader # Goobstation-WantedMenu
    access: [["Security"]]
  - type: UserInterface
    interfaces:
      enum.HumanoidMarkingModifierKey.Key:
        type: HumanoidMarkingModifierBoundUserInterface
      enum.StrippingUiKey.Key:
        type: StrippableBoundUserInterface
      enum.SetWantedVerbMenu.Key: # Goobstation-WantedMenu
        type: WantedMenuBoundUserInterface
      # Goobstation - changelings
      enum.StoreUiKey.Key:
        type: StoreBoundUserInterface
      enum.HereticLivingHeartKey.Key: # goob edit - heretics
        type: LivingHeartMenuBoundUserInterface
      enum.WizardTeleportUiKey.Key: # Goobstation - wizard
        type: WizardTeleportBoundUserInterface
        requireInputValidation: false
      enum.VampireMutationUiKey.Key:
        type: VampireMutationBoundUserInterface #Pirate
  - type: Puller
  - type: Speech
    speechSounds: Alto
  - type: DamageForceSay
  - type: Vocal
    sounds:
      Male: MaleHuman
      Female: FemaleHuman
      Unsexed: MaleHuman
  - type: LanguageKnowledge # Einstein Engines - Languages (all mobs should speak TauCeti unless otherwise specified, to prevent universal.)
    speaks:
    - TauCetiBasic
    understands:
    - TauCetiBasic
  - type: Emoting
  - type: BodyEmotes
    soundsId: GeneralBodyEmotes
  - type: Grammar
    attributes:
      proper: true
  - type: MobPrice
    price: 1500 # Kidnapping a living person and selling them for cred is a good move.
    deathPenalty: 0.01 # However they really ought to be living and intact, otherwise they're worth 100x less.
  - type: Tag
    tags:
    - CanPilot
    - FootstepSound
    - DoorBumpOpener
    - AnomalyHost
  - type: LayingDown # WD EDIT
  - type: Targeting # Shitmed Change
  - type: CanEscapeInventory # GoobStation - Escape being picked up by players
  - type: Consciousness
    cap: 190
    threshold: 95
  - type: Penetratable # Goobstation - Better snipers
    penetrateDamage: 50
    damagePenalty: 0.2
  - type: ExaminableCharacter # WWDP
  - type: AlternativeJob # Pirate - Alternative Jobs
  - type: OfferItem # Pirate - port EE item offesrs
  - type: Sprinter # Goobstation
  - type: SprintingAffectedByScale # Goobstation: port EE height/width sliders

- type: entity
  save: false
  parent:
  - MobBloodstream
  - MobRespirator
  - MobAtmosStandard
  - MobFlammable
  - BaseMobSpecies
  id: BaseMobSpeciesOrganic
  abstract: true
  components:
  - type: Absorbable # Goobstation - changelings
  - type: Barotrauma
    damage:
      types:
        Blunt: 0.50 #per second, scales with pressure and other constants.
        Heat: 0.1
  #- type: PassiveDamage # Slight passive regen. Assuming one damage type, comes out to about 4 damage a minute.
  #  allowedStates:
  #   - Alive
  #   damageCap: 20
  #   damage:
  #     types:
  #       Heat: -0.07
  #    groups:
  #      Brute: -0.07
  - type: Fingerprint
  - type: Blindable
  # Other
  - type: Temperature
    heatDamageThreshold: 325
    coldDamageThreshold: 260
    currentTemperature: 310.15
    specificHeat: 42
    coldDamage:
      types:
        Cold: 0.1 #per second, scales with temperature & other constants
    heatDamage:
      types:
        Heat: 1.5 #per second, scales with temperature & other constants
  - type: TemperatureSpeed
    thresholds:
      293: 0.8
      280: 0.6
      260: 0.4
  - type: ThermalRegulator
    metabolismHeat: 800
    radiatedHeat: 100
    implicitHeatRegulation: 500
    sweatHeatRegulation: 2000
    shiveringHeatRegulation: 2000
    normalBodyTemperature: 310.15
    thermalRegulationTemperatureThreshold: 2
  - type: Perishable
  - type: Butcherable
    butcheringType: Spike # TODO human.
    spawned:
      - id: FoodMeat
        amount: 5
  - type: Respirator
    damage:
      types:
        Asphyxiation: 1.0
    damageRecovery:
      types:
        Asphyxiation: -1.0
  - type: FireVisuals
    alternateState: Standing
  - type: FootprintOwner # Corvax-Next-Footprints
  - type: Carriable # Goobstation
  - type: CPRTraining # Goobstation - Just give to everyone! This ain't MRP.
  - type: BloodstreamAffectedByMass # Goobstation: port EE height/width sliders. Also, lol at the comment above.
    power: 0.6
  - type: FlightAffectedByScale # Goobstation: port EE height/width sliders.
  - type: StunVisuals

- type: entity
  save: false
  id: BaseSpeciesDummy
  parent: InventoryBase
  abstract: true
  components:
  - type: Hands
  - type: ComplexInteraction
  - type: ContainerContainer
  - type: Icon
    sprite: Mobs/Species/Human/parts.rsi
    state: full
  - type: Sprite
    drawdepth: Mobs
    noRot: true
    # TODO BODY Turn these into individual body parts?
    layers:
    - map: [ "enum.HumanoidVisualLayers.Chest" ]
    - map: [ "enum.HumanoidVisualLayers.Groin" ]
    - map: [ "enum.HumanoidVisualLayers.Head" ]
    - map: [ "enum.HumanoidVisualLayers.Snout" ]
    - map: [ "enum.HumanoidVisualLayers.Eyes" ]
    - map: [ "enum.HumanoidVisualLayers.RArm" ]
    - map: [ "enum.HumanoidVisualLayers.LArm" ]
    - map: [ "enum.HumanoidVisualLayers.RLeg" ]
    - map: [ "enum.HumanoidVisualLayers.LLeg" ]
    - map: [ "enum.HumanoidVisualLayers.Underwear" ]
    - map: [ "enum.HumanoidVisualLayers.Undershirt" ]
    - map: ["jumpsuit"]
    - map: ["enum.HumanoidVisualLayers.LFoot"]
    - map: ["enum.HumanoidVisualLayers.RFoot"]
    - map: ["enum.HumanoidVisualLayers.LHand"]
    - map: ["enum.HumanoidVisualLayers.RHand"]
    - map: ["enum.HumanoidVisualLayers.Handcuffs"]
      color: "#ffffff"
      sprite: Objects/Misc/handcuffs.rsi
      state: body-overlay-2
      visible: false
    - map: [ "enum.HumanoidVisualLayers.Face" ]
    - map: [ "gloves" ]
    - map: [ "shoes" ]
    - map: [ "ears" ]
    - map: [ "eyes" ]
    - map: [ "id" ]
    - map: [ "outerClothing" ]
    - map: [ "belt" ] #Goobedit - Belts over outerwear
    - map: [ "back" ]
    - map: [ "neck" ]
    - map: [ "enum.HumanoidVisualLayers.FacialHair" ]
    - map: [ "enum.HumanoidVisualLayers.HeadSide" ]
    - map: [ "maskalt" ]
    - map: [ "enum.HumanoidVisualLayers.Hair" ]
    - map: [ "mask" ]
    - map: [ "enum.HumanoidVisualLayers.HeadTop" ]
    - map: [ "enum.HumanoidVisualLayers.Tail" ]
    - map: [ "head" ]
    - map: [ "pocket1" ]
    - map: [ "pocket2" ]
  - type: Appearance
  - type: HumanoidAppearance
    species: Human
  - type: Body
    prototype: Human
    requiredLegs: 2
  - type: Absorbable # Goobstation - changelings
  - type: UserInterface
    interfaces:
      enum.HumanoidMarkingModifierKey.Key: # sure, this can go here too
        type: HumanoidMarkingModifierBoundUserInterface
