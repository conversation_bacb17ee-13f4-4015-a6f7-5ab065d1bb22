# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2022 Alex <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2022 fishfish458 <fishfish458>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  parent: CrateBaseWeldable
  id: CrateGenericSteel
  name: crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/generic.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/generic.rsi
  - type: Reflect
    reflects:
    - Energy
    reflectProb: 0.2
    spread: 90
  - type: Paintable
    group: CrateSteel
  - type: RadiationBlockingContainer
    # Goobstation Start - Radiation Overhaul
    # resistance: 2.5
    decay: 2.5
    # Goobstation End - Radiation Overhaul
  - type: PhysicalComposition #Goobstation - Recycle update
    materialComposition:
      Steel: 125

- type: entity
  parent: CrateBaseWeldable
  id: CratePlastic
  name: plastic crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/plastic.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/plastic.rsi
  - type: Construction
    graph: CratePlastic
    node: crateplastic
    containers:
    - entity_storage
  - type: StaticPrice
    price: 100
  - type: PhysicalComposition #Goobstation - Recycle update
    materialComposition:
      Plastic: 125
  - type: Paintable
    group: CratePlastic

- type: entity
  parent: CratePlastic
  id: CrateFreezer
  name: freezer
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/freezer.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/freezer.rsi
  - type: AntiRottingContainer
  - type: ExplosionResistance
    damageCoefficient: 0.50
  - type: Construction
    graph: CrateFreezer
    node: done
    containers:
    - entity_storage
  - type: Paintable
    group: null

- type: entity
  parent: CratePlastic
  id: CrateHydroponics
  name: hydroponics crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/hydroponics.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/hydroponics.rsi

- type: entity
  parent: CratePlastic
  id: CrateMedical
  name: medical crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/medical.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/medical.rsi

- type: entity
  parent: CrateGenericSteel
  id: CrateRadiation
  name: radiation gear crate
  description: Is not actually lead lined. Do not store your plutonium in this.
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/radiation.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/radiation.rsi

- type: entity
  parent: CratePlastic
  id: CrateInternals
  name: oxygen crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/o2.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/o2.rsi

- type: entity
  parent: CrateGenericSteel
  id: CrateElectrical
  name: electrical crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/electrical.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/electrical.rsi

- type: entity
  parent: CrateGenericSteel
  id: CrateEngineering
  name: engineering crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/engineering.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/engineering.rsi

- type: entity
  parent: CrateGenericSteel
  id: CrateScience
  name: science crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/science.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/science.rsi

- type: entity
  parent: CrateGenericSteel
  id: CrateSurgery
  name: surgery crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/surgery.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/surgery.rsi

- type: entity
  parent: CrateGeneric
  id: CrateWeb
  name: web crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/web.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/web.rsi
    layers:
    - state: base
      map: ["enum.StorageVisualLayers.Base"]
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Construction
    graph: WebStructures
    node: crate
  - type: Damageable
    damageModifierSet: Web
  - type: Destructible
    thresholds:
    - trigger: # Excess damage, don't spawn entities
        !type:DamageTrigger
        damage: 100
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWebSilk:
            min: 3
            max: 5
  - type: EntityStorage
    closeSound:
      path: /Audio/Effects/rustle1.ogg
    openSound:
      path: /Audio/Effects/rustle2.ogg

# Secure Crates

- type: entity
  parent: [ CrateBaseSecure, BaseSecurityContraband ]
  id: CrateSecgear
  name: secgear crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/sec_gear.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/sec_gear.rsi
  - type: AccessReader
    access: [["Security"]]

- type: entity
  parent: CrateBaseSecure
  id: CrateEngineeringSecure
  name: secure engineering crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/engicrate_secure.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/engicrate_secure.rsi
  - type: AccessReader
    access: [["Engineering"]]

- type: entity
  parent: CrateBaseSecure
  id: CrateMedicalSecure
  name: secure medical crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/medicalcrate_secure.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/medicalcrate_secure.rsi
  - type: AccessReader
    access: [["Medical"]]

- type: entity
  parent: CrateBaseSecure
  id: CrateChemistrySecure
  name: secure chemistry crate
  components:
    - type: Icon
      sprite: Structures/Storage/Crates/chemcrate_secure.rsi
    - type: Sprite
      sprite: Structures/Storage/Crates/chemcrate_secure.rsi
    - type: AccessReader
      access: [["Chemistry"]]

- type: entity
  parent: CrateBaseSecure
  id: CratePrivateSecure
  name: private crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/privatecrate_secure.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/privatecrate_secure.rsi

- type: entity
  parent: CrateBaseSecure
  id: CrateScienceSecure
  name: secure science crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/scicrate_secure.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/scicrate_secure.rsi
  - type: AccessReader
    access: [["Research"]]

- type: entity
  parent: CrateBaseSecure
  id: CratePlasma
  name: plasma crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/plasma.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/plasma.rsi
  - type: AccessReader
    access: [["Engineering"], ["Research"], ["Chemistry"]]

- type: entity
  parent: CrateBaseSecure
  id: CrateSecure
  name: secure crate
  components:
    - type: Icon
      sprite: Structures/Storage/Crates/secure.rsi
    - type: Sprite
      sprite: Structures/Storage/Crates/secure.rsi

- type: entity
  parent: CrateBaseSecure
  id: CrateHydroSecure
  name: secure hydroponics crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/hydro_secure.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/hydro_secure.rsi
  - type: AccessReader
    access: [["Hydroponics"]]

- type: entity
  parent: CrateBaseSecure
  id: CrateWeaponSecure
  name: secure weapon crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/weapon.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/weapon.rsi
  - type: AccessReader
    access: [["Armory"]]
  - type: Destructible #GOOB START
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: ["Destruction"]
      - !type:PlaySoundBehavior
        sound:
          collection: MetalBreak
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: StrongCrates

- type: entity
  parent: [ CrateBaseSecure, BaseSecurityContraband ]
  suffix: Armory, Secure
  id: CrateContrabandStorageSecure
  name: contraband storage crate
  description: An armory access locked crate for storing contraband confiscated from suspects or prisoners.
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/sec_gear.rsi
  - type: AccessReader
    access: [["Armory"]]

- type: entity
  parent: CrateBaseSecure
  id: CrateCommandSecure
  name: command crate
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/command.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/command.rsi
  - type: AccessReader
    access: [["Command"]]

- type: entity
  parent: CrateGeneric
  id: CrateLivestock
  name: livestock crate
  components:
  - type: EntityStorage
    airtight: false
  - type: Sprite
    sprite: Structures/Storage/Crates/livestock.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "-0.25,0.625"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/livestock.rsi
    state: base
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroyHeavy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 1
            max: 4
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Physics
    bodyType: Dynamic
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.29"
        density: 135
        mask:
        - CrateMask #this is so they can go under plastic flaps
        layer:
        - LargeMobLayer
  - type: Construction
    graph: CrateLivestock
    node: cratelivestock
    containers:
    - entity_storage
  - type: StaticPrice
    price: 125

- type: entity
  parent: CrateGeneric
  id: CrateRodentCage
  name: hamster cage
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/cage.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "0.0,0.125"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/cage.rsi
  - type: Destructible
    thresholds:
    - trigger: # Excess damage, don't spawn entities
        !type:DamageTrigger
        damage: 75
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: MetalGlassBreak
      - !type:SpawnEntitiesBehavior
        spawn:
          SheetPlastic:
            min: 1
            max: 2
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Physics
    bodyType: Dynamic
  - type: EntityStorage
    capacity: 1
    airtight: false
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.4,-0.4,0.4,0.29"
        density: 80
        mask:
        - CrateMask
        layer:
        - LargeMobLayer
  - type: StaticPrice
    price: 75

- type: entity
  id: CrateBaseLockBox
  parent: CrateBaseSecure
  name: lock box
  description: "A secure lock box. Funds from its sale will be distributed back to the department. Just remember: Cargo always takes a cut."
  abstract: true
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/lockbox.rsi
  - type: GenericVisualizer
    visuals:
      enum.PaperLabelVisuals.HasLabel:
        enum.PaperLabelVisuals.Layer:
          True: { visible: true }
          False: { visible: false }
      enum.PaperLabelVisuals.LabelType:
        enum.PaperLabelVisuals.Layer:
          Paper: { state: paper }
          Bounty: { state: bounty }
          CaptainsPaper: { state: captains_paper }
          Invoice: { state: invoice }
      enum.StorageVisuals.Open:
        lid_overlay:
          True: { visible: false }
          False: { visible: true }
  - type: OverrideSell
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.3,-0.4,0.3,0.19"
        density: 50
        mask:
        - CrateMask #this is so they can go under plastic flaps
        layer:
        - MachineLayer

- type: entity
  id: CrateLockBoxEngineering
  parent: CrateBaseLockBox
  name: engineering lock box
  components:
  - type: Sprite
    layers:
    - state: base
    - state: overlay
      color: "#ad8c27"
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: overlay-closed
      color: "#ad8c27"
      map: [ lid_overlay ]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
      shader: unshaded
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "-0.46875,0.03125"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: OverrideSell
    overrideAccount: Engineering
  - type: AccessReader
    access: [["Engineering"]]

- type: entity
  id: CrateLockBoxMedical
  parent: CrateBaseLockBox
  name: medical lock box
  components:
  - type: Sprite
    layers:
    - state: base
    - state: overlay
      color: "#92c7e8"
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: overlay-closed
      color: "#92c7e8"
      map: [ lid_overlay ]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
      shader: unshaded
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "-0.46875,0.03125"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: OverrideSell
    overrideAccount: Medical
  - type: AccessReader
    access: [["Medical"]]

- type: entity
  id: CrateLockBoxScience
  parent: CrateBaseLockBox
  name: science lock box
  components:
  - type: Sprite
    layers:
    - state: base
    - state: overlay
      color: "#ba4bf0"
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: overlay-closed
      color: "#ba4bf0"
      map: [ lid_overlay ]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
      shader: unshaded
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "-0.46875,0.03125"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: OverrideSell
    overrideAccount: Science
  - type: AccessReader
    access: [["Research"]]

- type: entity
  id: CrateLockBoxSecurity
  parent: CrateBaseLockBox
  name: security lock box
  components:
  - type: Sprite
    layers:
    - state: base
    - state: overlay
      color: "#c12d30"
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: overlay-closed
      color: "#c12d30"
      map: [ lid_overlay ]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
      shader: unshaded
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "-0.46875,0.03125"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: OverrideSell
    overrideAccount: Security
  - type: AccessReader
    access: [["Security"]]

- type: entity
  id: CrateLockBoxService
  parent: CrateBaseLockBox
  name: service lock box
  components:
  - type: Sprite
    layers:
    - state: base
    - state: overlay
      color: "#53b723"
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: overlay-closed
      color: "#53b723"
      map: [ lid_overlay ]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
      shader: unshaded
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "-0.46875,0.03125"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: OverrideSell
    overrideAccount: Service
  - type: AccessReader
    access: [["Service"]]

- type: entity
  parent: CrateGeneric
  id: CratePirate
  name: pirate chest
  description: A space pirate chest, not for station lubbers.
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/piratechest.rsi
    layers:
    - state: crate
      map: ["enum.StorageVisualLayers.Base"]
    - state: crate_door
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "0.0,-0.09375"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/piratechest.rsi
    state: crate_icon
  - type: Appearance
  - type: EntityStorageVisuals
    stateDoorOpen: crate_open
    stateDoorClosed: crate_door

- type: entity
  parent: CratePirate
  id: CrateToyBox
  name: toy box
  suffix: Empty
  description: A box overflowing with fun.
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/toybox.rsi
    layers:
    - state: crate
      map: ["enum.StorageVisualLayers.Base"]
    - state: crate_door
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: paper
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/toybox.rsi
    state: crate_icon

- type: entity
  parent: CrateGeneric
  id: CrateCoffin
  name: coffin
  description: A comfy coffin, excellent place for the vampires and corpses.
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/coffin.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/coffin.rsi
    state: base
  - type: Destructible
    thresholds:
    - trigger: # Excess damage, don't spawn entities
        !type:DamageTrigger
        damage: 75
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 15
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 1
            max: 4
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Construction
    graph: CrateCoffin
    node: cratecoffin
    containers:
    - entity_storage
  - type: Coffin # Pirate

- type: entity
  parent: CrateGeneric
  id: CrateWoodenGrave
  name: grave
  suffix: wooden
  description: Someone died here...
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/wooden_grave.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "-0.28125,0.625"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/wooden_grave.rsi
    state: base
  - type: Destructible
    thresholds:
    - trigger: # Excess damage, don't spawn entities
        !type:DamageTrigger
        damage: 400
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:DoActsBehavior
        acts: ["Destruction"]
    - trigger:
        !type:DamageTrigger
        damage: 200 # discourage just beating the grave to break it open
      behaviors:
      - !type:PlaySoundBehavior
        sound:
          collection: WoodDestroy
      - !type:SpawnEntitiesBehavior
        spawn:
          MaterialWoodPlank1:
            min: 1
            max: 4
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: Physics
    bodyType: Static
  - type: Grave
  - type: ProRottingContainer
  - type: EntityStorage
    airtight: true
    isCollidableWhenOpen: false
    closeSound:
      path: /Audio/Items/shovel_dig.ogg
    openSound:
      path: /Audio/Items/shovel_dig.ogg

- type: entity
  parent: CrateWoodenGrave
  id: CrateStoneGrave
  name: grave
  suffix: stone
  description: Someone died here...
  components:
  - type: Sprite
    sprite: Structures/Storage/Crates/stone_grave.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "-0.3125,0.5625"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Icon
    sprite: Structures/Storage/Crates/stone_grave.rsi
    state: base

- type: entity
  parent: CrateGenericSteel
  id: CrateSyndicate
  name: Syndicate crate
  description: A dark steel crate with red bands and a letter S embossed on the front.
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/syndicate.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/syndicate.rsi

- type: entity
  parent: [StructureWheeled, CrateBaseWeldable]
  id: CrateTrashCart
  name: trash cart
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/trashcart.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/trashcart.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "0.0,0.03125"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: Paintable
    group: null

- type: entity
  parent: CrateBaseSecure
  id: CrateTrashCartJani
  name: janitorial trash cart
  components:
  - type: Icon
    sprite: Structures/Storage/Crates/trashcart_jani.rsi
  - type: Sprite
    sprite: Structures/Storage/Crates/trashcart_jani.rsi
    layers:
    - state: base
    - state: closed
      map: ["enum.StorageVisualLayers.Door"]
    - state: welded
      visible: false
      map: ["enum.WeldableLayers.BaseWelded"]
    - state: locked
      map: ["enum.LockVisualLayers.Lock"]
      shader: unshaded
    - state: paper
      sprite: Structures/Storage/Crates/labels.rsi
      offset: "0.0,0.03125"
      map: ["enum.PaperLabelVisuals.Layer"]
  - type: AccessReader
    access: [["Janitor"]]
  - type: Paintable
    group: null

- type: entity
  parent: CrateBaseWeldable
  id: InvisibleCrate
  suffix: Stealth
  components:
  - type: Stealth
    hadOutline: true
  - type: StealthOnMove
    passiveVisibilityRate: -0.10
    movementVisibilityRate: 0.10
