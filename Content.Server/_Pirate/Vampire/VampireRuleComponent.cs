using Robust.Shared.Prototypes;

namespace Content.Server.GameTicking.Rules.Components;

/// <summary>
/// Proxy component for VampireRuleComponent from Content.Pirate.Server
/// This allows AdminVerbSystem.Antags.cs to reference the vampire rule component
/// without having access to the full Content.Pirate.Server assembly
/// </summary>
[RegisterComponent]
public sealed partial class VampireRuleComponent : Component
{
    // This is intentionally empty - it's just a proxy for the real component
    // The actual implementation is in Content.Pirate.Server/GameTicking/Rules/Components/VampireRuleComponent.cs
}
